package com.qiguliuxing.dts.admin.web;

import com.alibaba.fastjson.JSONObject;
import com.qiguliuxing.dts.admin.annotation.RequiresPermissionsDesc;
import com.qiguliuxing.dts.admin.service.AdminDataAuthService;
import com.qiguliuxing.dts.admin.util.AuthSupport;
import com.qiguliuxing.dts.core.util.ResponseUtil;
import com.qiguliuxing.dts.core.validator.Order;
import com.qiguliuxing.dts.core.validator.Sort;
import com.qiguliuxing.dts.db.domain.DtsGoods;
import com.qiguliuxing.dts.db.domain.DtsGroupon;
import com.qiguliuxing.dts.db.domain.DtsGrouponRules;
import com.qiguliuxing.dts.db.service.DtsGoodsService;
import com.qiguliuxing.dts.db.service.DtsGrouponRulesService;
import com.qiguliuxing.dts.db.service.DtsGrouponService;
import com.qiguliuxing.dts.db.util.PageResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.qiguliuxing.dts.db.util.PageResult.emptyPage;

@RestController
@RequestMapping("/admin/groupon")
@Validated
@Slf4j
@AllArgsConstructor
public class AdminGrouponController {

    private final DtsGrouponRulesService rulesService;
    private final DtsGoodsService goodsService;
    private final DtsGrouponService grouponService;
    private final AdminDataAuthService adminDataAuthService;

    @RequiresPermissions("admin:groupon:read")
    @RequiresPermissionsDesc(menu = {"推广管理", "团购管理"}, button = "详情")
    @GetMapping("/listRecord")
    public Object listRecord(@RequestParam(required = false) String rulesId,
                             @RequestParam(defaultValue = "1") Integer page,
                             @RequestParam(defaultValue = "10") Integer limit,
                             @Sort @RequestParam(defaultValue = "add_time") String sort,
                             @Order @RequestParam(defaultValue = "desc") String order) {
        if (log.isDebugEnabled()) {
            log.debug("【请求开始】操作人:[{}] 推广管理->团购管理->详情,请求参数:rulesId:{},page:{}", AuthSupport.userName(), rulesId, page);
        }

        // 需要区分数据权限，如果属于品牌商管理员，则需要获取当前用户管理品牌店铺
        List<Integer> brandIds = null;
        if (adminDataAuthService.isBrandManager()) {
            brandIds = adminDataAuthService.getBrandIds();
            if (log.isDebugEnabled()) {
                log.debug("运营商管理角色操作，需控制数据权限，brandIds:{}", JSONObject.toJSONString(brandIds));
            }

            if (brandIds == null || brandIds.size() == 0) {// 如果尚未管理任何入驻店铺，则返回空数据
                Map<String, Object> data = new HashMap<>();
                data.put("total", 0L);
                data.put("items", null);

                if (log.isDebugEnabled()) {
                    log.debug("【请求结束】推广管理->团购管理->详情,响应结果:{}", JSONObject.toJSONString(data));
                }
                return ResponseUtil.ok(data);
            }
        }

        PageResult<DtsGroupon> grouponList;
        long total;
        if (brandIds == null || brandIds.isEmpty()) {
            grouponList = grouponService.querySelective(rulesId, page, limit, sort, order);
            total = grouponList.getTotal();
        } else {
            grouponList = grouponService.queryBrandGroupons(brandIds, rulesId, page, limit, sort, order);
            total = grouponList.getTotal();
        }

        List<Map<String, Object>> records = new ArrayList<>();
        for (DtsGroupon groupon : grouponList.getItems()) {
            try {
                Map<String, Object> RecordData = new HashMap<>();
                List<DtsGroupon> subGrouponList = grouponService.queryJoinRecord(groupon.getId());
                DtsGrouponRules rules = rulesService.queryById(groupon.getRulesId());
                if (rules != null) {
                    DtsGoods goods = goodsService.findById(rules.getGoodsId().intValue());
                    RecordData.put("groupon", groupon);
                    RecordData.put("subGroupons", subGrouponList);
                    RecordData.put("rules", rules);
                    RecordData.put("goods", goods);
                    records.add(RecordData);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }

        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", records);

        if (log.isDebugEnabled()) {
            log.debug("【请求结束】推广管理->团购管理->详情,响应结果:{}", JSONObject.toJSONString(data));
        }
        return ResponseUtil.ok(data);
    }

    @RequiresPermissions("admin:groupon:list")
    @RequiresPermissionsDesc(menu = {"推广管理", "团购管理"}, button = "查询")
    @GetMapping("/list")
    public Object list(String goodsId, @RequestParam(defaultValue = "1") Integer page,
                       @RequestParam(defaultValue = "10") Integer limit,
                       @Sort @RequestParam(defaultValue = "add_time") String sort,
                       @Order @RequestParam(defaultValue = "desc") String order) {
        if (log.isDebugEnabled()) {
            log.debug("【请求开始】操作人:[{}] 推广管理->团购管理->查询,请求参数:goodsId:{},page:{}", AuthSupport.userName(), goodsId, page);
        }

        // 需要区分数据权限，如果属于品牌商管理员，则需要获取当前用户管理品牌店铺
        List<Integer> brandIds = null;
        if (adminDataAuthService.isBrandManager()) {
            brandIds = adminDataAuthService.getBrandIds();
            if (log.isDebugEnabled()) {
                log.debug("运营商管理角色操作，需控制数据权限，brandIds:{}", JSONObject.toJSONString(brandIds));
            }

            if (brandIds == null || brandIds.isEmpty()) {// 如果尚未管理任何入驻店铺，则返回空数据

                if (log.isDebugEnabled()) {
                    log.debug("【请求结束】推广管理->团购管理->查询,响应结果: 空");
                }
                return ResponseUtil.ok(emptyPage());
            }
        }

        PageResult<DtsGrouponRules> rulesList;
        if (brandIds == null) {
            rulesList = rulesService.querySelective(goodsId, page, limit, sort, order);
        } else {
            rulesList = rulesService.queryBrandGrouponRules(brandIds, goodsId, page, limit, sort, order);
        }

        if (log.isDebugEnabled()) {
            log.debug("【请求结束】推广管理->团购管理->查询,响应结果:{}", JSONObject.toJSONString(rulesList));
        }
        return ResponseUtil.ok(rulesList);
    }

    private Object validate(DtsGrouponRules grouponRules) {
        Long goodsId = grouponRules.getGoodsId();
        if (goodsId == null) {
            return ResponseUtil.badArgument();
        }
        BigDecimal discount = grouponRules.getDiscount();
        if (discount == null) {
            return ResponseUtil.badArgument();
        }
        Integer discountMember = grouponRules.getDiscountMember();
        if (discountMember == null) {
            return ResponseUtil.badArgument();
        }
        LocalDateTime expireTime = grouponRules.getExpireTime();
        if (expireTime == null) {
            return ResponseUtil.badArgument();
        }

        return null;
    }

    @RequiresPermissions("admin:groupon:update")
    @RequiresPermissionsDesc(menu = {"推广管理", "团购管理"}, button = "编辑")
    @PostMapping("/update")
    public Object update(@RequestBody DtsGrouponRules grouponRules) {
        if (log.isDebugEnabled()) {
            log.debug("【请求开始】操作人:[{}] 推广管理->团购管理->编辑,请求参数:{}", AuthSupport.userName(), JSONObject.toJSONString(grouponRules));
        }

        Object error = validate(grouponRules);
        if (error != null) {
            return error;
        }

        Integer goodsId = grouponRules.getGoodsId().intValue();
        DtsGoods goods = goodsService.findById(goodsId);
        if (goods == null) {
            return ResponseUtil.badArgumentValue();
        }

        grouponRules.setGoodsName(goods.getName());
        grouponRules.setPicUrl(goods.getPicUrl());

        if (rulesService.updateById(grouponRules) == 0) {
            if (log.isDebugEnabled()) {
                log.debug("推广管理->团购管理->编辑 失败：{}", "更新数据出错！");
            }
            return ResponseUtil.updatedDataFailed();
        }

        if (log.isDebugEnabled()) {
            log.debug("【请求结束】推广管理->团购管理->编辑,响应结果: 成功！");
        }
        return ResponseUtil.ok();
    }

    @RequiresPermissions("admin:groupon:create")
    @RequiresPermissionsDesc(menu = {"推广管理", "团购管理"}, button = "添加")
    @PostMapping("/create")
    public Object create(@RequestBody DtsGrouponRules grouponRules) {
        if (log.isDebugEnabled()) {
            log.debug("【请求开始】操作人:[{}] 推广管理->团购管理->添加,请求参数:{}", AuthSupport.userName(), JSONObject.toJSONString(grouponRules));
        }

        Object error = validate(grouponRules);
        if (error != null) {
            return error;
        }
        Long goodsId = grouponRules.getGoodsId();
        DtsGoods goods = null;
        /**
         * 如果输入的值为INT范围内，则先用goodsId找,如果超出范围，
         * 如果未找到，则转换为goodsSn找再找商品
         */
        if (goodsId.intValue() < Integer.MAX_VALUE) {
            goods = goodsService.findById(goodsId.intValue());
        }
        if (goods == null) {
            goods = goodsService.findByGoodsSn(goodsId.toString());
        }

        if (goods == null) {
            return ResponseUtil.badArgumentValue();
        }

        grouponRules.setGoodsId(goods.getId().longValue());// 最终存库只存商品id
        grouponRules.setGoodsName(goods.getName());
        grouponRules.setPicUrl(goods.getPicUrl());

        rulesService.createRules(grouponRules);

        if (log.isDebugEnabled()) {
            log.debug("【请求结束】推广管理->团购管理->添加,响应结果:{}", JSONObject.toJSONString(grouponRules));
        }
        return ResponseUtil.ok(grouponRules);
    }

    @RequiresPermissions("admin:groupon:delete")
    @RequiresPermissionsDesc(menu = {"推广管理", "团购管理"}, button = "删除")
    @PostMapping("/delete")
    public Object delete(@RequestBody DtsGrouponRules grouponRules) {
        if (log.isDebugEnabled()) {
            log.debug("【请求开始】操作人:[{}] 推广管理->团购管理->删除,请求参数:{}", AuthSupport.userName(), JSONObject.toJSONString(grouponRules));
        }

        Integer id = grouponRules.getId();
        if (id == null) {
            return ResponseUtil.badArgument();
        }

        rulesService.delete(id);

        if (log.isDebugEnabled()) {
            log.debug("【请求结束】推广管理->团购管理->删除,响应结果: 成功！");
        }
        return ResponseUtil.ok();
    }
}
