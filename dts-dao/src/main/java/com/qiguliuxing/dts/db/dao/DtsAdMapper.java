package com.qiguliuxing.dts.db.dao;

import com.qiguliuxing.dts.db.domain.DtsAd;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DtsAdMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(DtsAd dtsAd);

    int insertSelective(DtsAd dtsAd);

    DtsAd selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DtsAd dtsAd);

    int updateByPrimaryKey(DtsAd dtsAd);

    int logicalDeleteByPrimaryKey(Integer id);

    List<DtsAd> findByPositionAndEnabled(@Param("position") int i, @Param("enabled") int i1);

    List<DtsAd> querySelective(@Param("name") String name, @Param("content") String content,
                               @Param("orderByClause") String orderByClause,
                               @Param("limit") Integer limit, @Param("offset") Integer offset);

    int countByNameAndContent(@Param("name") String name, @Param("content") String content);
}
