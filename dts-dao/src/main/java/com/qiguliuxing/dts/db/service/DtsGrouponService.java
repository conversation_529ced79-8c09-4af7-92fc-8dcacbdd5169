package com.qiguliuxing.dts.db.service;

import com.qiguliuxing.dts.db.dao.DtsGrouponMapper;
import com.qiguliuxing.dts.db.dao.ex.GrouponMapperEx;
import com.qiguliuxing.dts.db.domain.DtsGroupon;
import com.qiguliuxing.dts.db.util.PageResult;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static com.qiguliuxing.dts.db.consts.OrderCommissionStatus.TRUE;

/**
 * 团购服务
 * <p>
 * 该服务负责处理团购相关的业务逻辑，包括创建团购、查询团购、统计团购人数等功能。
 * </p>
 */
@Service
@AllArgsConstructor
public class DtsGrouponService {

    private final DtsGrouponMapper dtsGrouponMapper;
    private final GrouponMapperEx grouponMapperEx;

    /**
     * 获取用户发起的团购记录
     *
     * @param userId 用户ID
     * @return 用户发起的团购记录列表
     */
    public List<DtsGroupon> queryMyGroupon(Integer userId) {
        return dtsGrouponMapper.findGrouponByUserIdAndPayStatus(null, userId, 0, TRUE);
    }

    /**
     * 获取用户参与的团购记录
     *
     * @param userId 用户ID
     * @return 用户参与的团购记录列表
     */
    public List<DtsGroupon> queryMyJoinGroupon(Integer userId) {
        return dtsGrouponMapper.findGrouponByUserIdAndPayStatus(null, userId, -1, TRUE);
    }

    /**
     * 根据OrderId查询团购记录
     *
     * @param orderId 订单ID
     * @return 对应的团购记录
     */
    public DtsGroupon queryByOrderId(Integer orderId) {
        return dtsGrouponMapper.findByOrderIdAndGrouponId(orderId, null);
    }

    /**
     * 获取某个团购活动参与的记录
     *
     * @param id 团购活动ID
     * @return 参与该团购活动的记录列表
     */
    public List<DtsGroupon> queryJoinRecord(Integer id) {
        return dtsGrouponMapper.findGrouponByUserIdAndPayStatus(null, null, id, null);
    }

    /**
     * 根据ID查询团购记录
     *
     * @param id 团购记录ID
     * @return 对应的团购记录
     */
    public DtsGroupon queryById(Integer id) {
        return dtsGrouponMapper.findByOrderIdAndGrouponId(null, id);
    }

    /**
     * 返回某个发起的团购参与人数
     *
     * @param grouponId 团购ID
     * @return 参与人数
     */
    public Long countGroupon(Integer grouponId) {
        return dtsGrouponMapper.countByGrouponId(grouponId);
    }

    /**
     * 更新团购记录
     *
     * @param groupon 需要更新的团购记录
     * @return 影响的行数
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public int updateById(DtsGroupon groupon) {
        groupon.setUpdateTime(LocalDateTime.now());
        return dtsGrouponMapper.updateByPrimaryKeySelective(groupon);
    }

    /**
     * 创建或参与一个团购
     * <p>
     * 该方法会开启事务，确保数据一致性
     * </p>
     *
     * @param groupon 团购记录信息
     * @return 影响的行数
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public int createGroupon(DtsGroupon groupon) {
        groupon.setAddTime(LocalDateTime.now());
        groupon.setUpdateTime(LocalDateTime.now());
        return dtsGrouponMapper.insertSelective(groupon);
    }

    /**
     * 查询所有发起的团购记录
     *
     * @param rulesId 团购规则ID
     * @param page    页码
     * @param size    每页数量
     * @param sort    排序字段
     * @param order   排序方式（升序或降序）
     * @return 分页后的团购记录
     */
    public PageResult<DtsGroupon> querySelective(String rulesId, Integer page, Integer size, String sort, String order) {
        long total;
        if (NumberUtils.isParsable(rulesId)) {
            total = dtsGrouponMapper.countByGrouponIdAndRulesId(0, Integer.parseInt(rulesId));
        } else {
            total = dtsGrouponMapper.countByGrouponIdAndRulesId(0, null);
        }
        page = (page != null && page > 0) ? page : 1;
        int offset = (page - 1) * size;

        List<DtsGroupon> result;
        if (NumberUtils.isParsable(rulesId)) {
            result = dtsGrouponMapper.findByGrouponIdAndRulesId(0, Integer.parseInt(rulesId), offset, size,
                    sort, order);
        } else {
            result = dtsGrouponMapper.findByGrouponIdAndRulesId(0, null, offset, size,
                    sort, order);
        }
        return PageResult.of(total, result, page, size);
    }

    /**
     * 根据品牌入驻店铺获取对应的团购数据
     *
     * @param brandIds 品牌ID列表
     * @param rulesId  团购规则ID
     * @param page     页码
     * @param size     每页数量
     * @param sort     排序字段
     * @param order    排序方式（升序或降序）
     * @return 分页后的团购记录
     */
    public PageResult<DtsGroupon> queryBrandGroupons(List<Integer> brandIds, String rulesId, Integer page, Integer size,
                                                     String sort, String order) {
        String orderBySql = null;
        if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(order)) {
            orderBySql = "o." + sort + " " + order;
        }

        String brandIdsSql = null;
        if (brandIds != null) {
            brandIdsSql = "";
            for (Integer brandId : brandIds) {
                brandIdsSql += "," + brandId;
            }
            brandIdsSql = " and g.brand_id in (" + brandIdsSql.substring(1) + ") ";
        }

        // Manual pagination
        page = (page != null && page > 0) ? page : 1;
        int offset = (page - 1) * size;

        // Get total count
        long total = grouponMapperEx.countBrandGroupons(rulesId, brandIdsSql);

        // Get paginated data
        List<DtsGroupon> result = grouponMapperEx.queryBrandGroupons(rulesId, orderBySql, brandIdsSql, size, offset);

        return PageResult.of(total, result, page, size);
    }
}
