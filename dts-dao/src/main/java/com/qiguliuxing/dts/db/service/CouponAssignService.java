package com.qiguliuxing.dts.db.service;

import com.qiguliuxing.dts.db.domain.DtsCoupon;
import com.qiguliuxing.dts.db.domain.DtsCouponUser;
import com.qiguliuxing.dts.db.util.CouponConstant;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class CouponAssignService {

    private final DtsCouponUserService couponUserService;
    private final DtsCouponService couponService;

    /**
     * 分发注册优惠券
     */
    public void assignForRegister(Integer userId) {
        List<DtsCoupon> couponList = couponService.queryRegister();
        for (DtsCoupon coupon : couponList) {
            Integer couponId = coupon.getId();

            Integer count = couponUserService.countUserAndCoupon(userId, couponId);
            if (count > 0) {
                continue;
            }

            Integer limit = coupon.getLimit();
            List<DtsCouponUser> couponUsers = new ArrayList<>(limit);
            while (limit > 0) {
                DtsCouponUser couponUser = new DtsCouponUser();
                couponUser.setCouponId(couponId);
                couponUser.setUserId(userId);
                Integer timeType = coupon.getTimeType();
                if (timeType.equals(CouponConstant.TIME_TYPE_TIME)) {
                    couponUser.setStartTime(coupon.getStartTime());
                    couponUser.setEndTime(coupon.getEndTime());
                } else {
                    LocalDate now = LocalDate.now();
                    couponUser.setStartTime(now);
                    couponUser.setEndTime(now.plusDays(coupon.getDays()));
                }
                couponUsers.add(couponUser);

                limit--;
            }

            couponUserService.insertBatch(couponUsers);

        }

    }

}
