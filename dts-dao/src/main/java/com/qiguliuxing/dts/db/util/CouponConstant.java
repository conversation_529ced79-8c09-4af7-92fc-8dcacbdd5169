package com.qiguliuxing.dts.db.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CouponConstant {
    public static final Integer TYPE_COMMON = 0;
    public static final Integer TYPE_REGISTER = 1;
    public static final Integer TYPE_CODE = 2;

    public static final Integer GOODS_TYPE_ALL = 0;
    public static final Integer GOODS_TYPE_CATEGORY = 1;
    public static final Integer GOODS_TYPE_ARRAY = 2;

    public static final Integer STATUS_NORMAL = 0;
    public static final Integer STATUS_EXPIRED = 1;
    public static final Integer STATUS_OUT = 2;

    public static final Integer TIME_TYPE_DAYS = 0;
    public static final Integer TIME_TYPE_TIME = 1;
}
