package com.qiguliuxing.dts.db.service;

import com.qiguliuxing.dts.db.bean.AccountTraceQueryDTO;
import com.qiguliuxing.dts.db.dao.DtsAccountTraceMapper;
import com.qiguliuxing.dts.db.dao.DtsUserAccountMapper;
import com.qiguliuxing.dts.db.dao.DtsUserMapper;
import com.qiguliuxing.dts.db.dao.ex.AccountMapperEx;
import com.qiguliuxing.dts.db.domain.DtsAccountTrace;
import com.qiguliuxing.dts.db.domain.DtsOrder;
import com.qiguliuxing.dts.db.domain.DtsUser;
import com.qiguliuxing.dts.db.domain.DtsUserAccount;
import com.qiguliuxing.dts.db.util.PageResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Collections.emptyList;

@Slf4j
@Service
@AllArgsConstructor
public class DtsAccountService {

    public static final long TWO_MONTH_DAYS = 60;//近两个月,60天

    public static final long ONE_WEEK_DAYS = 7;//近一周

    private final DtsUserAccountMapper userAccountMapper;
    private final DtsAccountTraceMapper accountTraceMapper;
    private final AccountMapperEx accountMapperEx;
    private final DtsUserMapper userMapper;

    public DtsUserAccount findShareUserAccountByUserId(Integer shareUserId) {

        List<DtsUserAccount> accounts = userAccountMapper.selectByUserId(shareUserId);
        if (accounts.size() == 1) {
            return accounts.get(0);
        } else {
            log.error("根据代理用户id：{},获取账号信息出错!!!", shareUserId);
            return null;
        }
    }

    public List<Integer> findAllSharedUserId() {
        return accountMapperEx.getShareUserId();
    }

    private String getRandomNum(Integer num) {
        String base = "**********";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < num; i++) {
            int number = RandomUtils.nextInt(0, 10);
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setSettleMentAccount(Integer sharedUserId, String prevMonthEndDay, Integer type) throws Exception {
        // 1.获取用户的代理订单代理金额
        String endTime = prevMonthEndDay + " 23:59:59";
        String startTime = prevMonthEndDay.substring(0, 7) + "-01 00:00:00";
        BigDecimal toSettleMoney = accountMapperEx.getToSettleMoney(sharedUserId, startTime, endTime);
        if (toSettleMoney == null || toSettleMoney.compareTo(new BigDecimal(0)) <= 0) {//如果无佣金
            toSettleMoney = new BigDecimal(0);
        }
        log.info("代理用户编号： {" + sharedUserId + "},日期：" + startTime + " - " + endTime + ",获取佣金: " + toSettleMoney
                 + "元");

        if (toSettleMoney.compareTo(new BigDecimal(0)) > 0) {
            settleApplyTrace(sharedUserId, startTime, endTime, type, toSettleMoney, null);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void settleApplyTrace(Integer sharedUserId, String startTime, String endTime, Integer type, BigDecimal toSettleMoney, String mobile) {

        int settlementRate = 5;
        // 获取用户账户信息并更新记录
        DtsUserAccount account = this.findShareUserAccountByUserId(sharedUserId);
        if (account == null) {
            return;
        }
        if (toSettleMoney.compareTo(new BigDecimal("0")) == 0) {// 如果该用户未产生推荐单，则降低结算比例
            settlementRate = account.getSettlementRate() > 8 ? 8 : account.getSettlementRate();
        }

        // 更新订单结算状态
        accountMapperEx.setLastMonthOrderSettleStaus(sharedUserId, startTime, endTime);

        //更新代理用户账号信息
        account.setRemainAmount(account.getRemainAmount().add(toSettleMoney));//剩余结算,尚未结算给用户
        account.setTotalAmount(account.getTotalAmount().add(toSettleMoney));
        account.setModifyTime(LocalDateTime.now());
        account.setSettlementRate(settlementRate);
        userAccountMapper.updateByPrimaryKeySelective(account);

        // 新增账户跟踪表，添加结算跟踪记录
        DtsAccountTrace trace = new DtsAccountTrace();
        trace.setAmount(account.getRemainAmount());//当前申请金额，直接将未结算的进行申请
        trace.setTotalAmount(account.getTotalAmount());//已提现总金额
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        String now = df.format(LocalDate.now());
        String traceSn = now + getRandomNum(6);
        trace.setTraceSn(traceSn);
        trace.setAddTime(LocalDateTime.now());
        trace.setType(type);
        trace.setUserId(sharedUserId);
        trace.setStatus(0);//申请状态
        trace.setMobile(mobile);
        accountTraceMapper.insert(trace);
    }

    /**
     * 统计某个用户时间段内的结算金额
     */
    public BigDecimal getMonthSettleMoney(Integer sharedUserId, String startTime, String endTime) {
        BigDecimal staticSettleMoney = accountMapperEx.staticMonthSettleMoney(sharedUserId, startTime, endTime);
        if (staticSettleMoney == null || staticSettleMoney.compareTo(new BigDecimal("0")) == 0) {// 如果该用户未产生推荐单，则降低结算比例
            staticSettleMoney = BigDecimal.valueOf(0.00);
        }
        return staticSettleMoney;
    }

    public Map<String, Object> getStatistics(Integer sharedUserId, int dayAgo) {
        Map<String, Object> result = new HashMap<>();
        LocalDateTime startTime = LocalDateTime.now().minusDays(dayAgo);

        // We don't need to use DtsUserExample anymore
        Long userCnt = userMapper.countByShareUserIdAndTime(sharedUserId, startTime);

        Long orderCnt = accountMapperEx.countOrderSharedUser(sharedUserId, startTime);
        BigDecimal orderSettleAmt = accountMapperEx.sumOrderSettleAmtSharedUser(sharedUserId, startTime);
        if (orderSettleAmt == null) {
            orderSettleAmt = new BigDecimal("0.00");
        }
        BigDecimal finalSettleAmt = orderSettleAmt; //默认就是设置的结算价格
        result.put("userCnt", userCnt);
        result.put("orderCnt", orderCnt);
        result.put("orderSettleAmt", orderSettleAmt);
        result.put("finalSettleAmt", finalSettleAmt);
        return result;
    }

    public PageResult<DtsOrder> querySettlementOrder(Integer sharedUserId, List<Integer> orderStatus,
                                                     List<Integer> settlementStatus, Integer page, Integer size) {
        StringBuilder conditionSql = new StringBuilder();
        if (orderStatus != null) {
            for (Integer orderStatu : orderStatus) {
                conditionSql.append(",").append(orderStatu);
            }
            conditionSql.append("and o.order_status in (").append(conditionSql.substring(1)).append(") ");
        }
        if (settlementStatus != null && settlementStatus.size() == 1) {
            conditionSql.append(" and o.settlement_status =").append(settlementStatus.get(0)).append(" ");
        }

        String sql = conditionSql.toString();
        int total = accountMapperEx.countSettlementOrder(sharedUserId, sql);
        if (total < 1) {
            return PageResult.of(0, emptyList(), page, size);
        }

        int offset = (page - 1) * size;
        List<DtsOrder> data = accountMapperEx.querySettlementOrder(sharedUserId, sql, size, offset);
        return PageResult.of(total, data, page, size);
    }

    public PageResult<DtsAccountTrace> queryAccountTraceList(Integer userId, Integer page, Integer size) {
        int total = accountTraceMapper.countByUserId(userId);
        if (total < 1) {
            return PageResult.of(0, emptyList(), page, size);
        }
        int offset = (page - 1) * size;
        List<DtsAccountTrace> data = accountTraceMapper.queryByUserId(userId, size, offset);
        return PageResult.of(total, data, page, size);
    }

    /**
     * 新增申请提现记录
     *
     * @param userId
     * @param applyAmt
     */
    public void addExtractRecord(Integer userId, BigDecimal applyAmt, String mobile, String smsCode,
                                 BigDecimal hasAmount) {
        DtsAccountTrace dtsAccountTrace = new DtsAccountTrace();
        dtsAccountTrace.setAmount(applyAmt);
        dtsAccountTrace.setMobile(mobile);
        dtsAccountTrace.setTotalAmount(applyAmt.add(hasAmount));
        dtsAccountTrace.setSmsCode(smsCode);

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        String now = df.format(LocalDate.now());
        String traceSn = now + getRandomNum(6);
        dtsAccountTrace.setTraceSn(traceSn);

        dtsAccountTrace.setAddTime(LocalDateTime.now());
        dtsAccountTrace.setType(1);// 申请中..
        dtsAccountTrace.setUserId(userId);
        accountTraceMapper.insert(dtsAccountTrace);
    }

    public void add(DtsUserAccount userAccount) {
        userAccount.setCreateTime(LocalDateTime.now());
        userAccount.setModifyTime(LocalDateTime.now());
        userAccountMapper.insert(userAccount);
    }

    /**
     * 根据账号和状态，查询提现记录
     */
    public List<DtsAccountTrace> getAccountTraceList(Integer userId, Integer... types) {
        if (userId == null || types == null || types.length < 1) {
            return emptyList();
        }
        List<Integer> statusList = Arrays.asList(types);
        // We need to implement a new method in the mapper
        // For now, we'll use the existing method
        return accountTraceMapper.queryByUserIdAndStatus(userId, statusList);
    }

    public PageResult<DtsAccountTrace> querySelectiveTrace(AccountTraceQueryDTO dto) {
        //是否有匹配到的用户,转用户id集合

        int total = accountTraceMapper.countByUserIdAndStatus(dto.getUserIdArray(), dto.getStatus());
        if (total < 1) {
            return PageResult.of(0, emptyList(), dto.getPage(), dto.getLimit());
        }

        List<DtsAccountTrace> data = accountTraceMapper.querySelectiveTrace(dto);
        return PageResult.of(total, data, dto.getPage(), dto.getLimit());
    }

    /**
     * 只计算近两个月内未结算的订单佣金
     * 时间范围两月内，且订单超过一周，原因，一周内可能发生退款，
     * 减少退款订单对佣金结算的影响
     */
    public BigDecimal getUnSettleAmount(Integer userId) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(TWO_MONTH_DAYS);
        LocalDateTime endTime = LocalDateTime.now().minusDays(ONE_WEEK_DAYS);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return getUnSettleAmount(userId, startTime.format(df), endTime.format(df));
    }

    public BigDecimal getUnSettleAmount(Integer userId, String startDay, String endDay) {
        BigDecimal staticSettleMoney = accountMapperEx.getToSettleMoney(userId, startDay, endDay);
        if (staticSettleMoney == null || staticSettleMoney.compareTo(new BigDecimal("0")) == 0) {// 如果该用户未产生推荐单，则降低结算比例
            staticSettleMoney = new BigDecimal("0.00");
        }
        log.info("获取开始时间：{} - 结束时间 ：{} 内 用户id:{} 的未结算佣金 :{}", startDay, endDay, userId, staticSettleMoney);
        return staticSettleMoney;
    }

    /**
     * 为资金账户的安全，建议做线下销账处理，处理后执行该逻辑
     * 这里只根据记录做状态调整和审批意见记录
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean approveAccountTrace(Integer traceId, Integer status, String traceMsg) {

        DtsAccountTrace trace = accountTraceMapper.selectByPrimaryKey(traceId);
        trace.setStatus(status);
        trace.setTraceMsg(traceMsg);
        if (status == 1) {//如果是审批通过，需要消除账户中的可提现余额
            DtsUserAccount userAccount = findShareUserAccountByUserId(trace.getUserId());
            if (userAccount != null) {
                userAccount.setRemainAmount(userAccount.getRemainAmount().subtract(trace.getAmount()));
                log.info("提现审批通过,调整账户可提现余额为{} - {} = {}", userAccount.getRemainAmount(), trace.getAmount(), userAccount.getRemainAmount().subtract(trace.getAmount()));
                if (userAccountMapper.updateByPrimaryKeySelective(userAccount) == 0) {
                    return false;
                }
            } else {
                log.error("审批提现，获取账号出错！请检查对应用户 userId:{} 的账户", trace.getUserId());
                return false;
            }
        }
        if (accountTraceMapper.updateByPrimaryKeySelective(trace) == 0) {
            return false;
        }
        return true;
    }

    /**
     * 根据用户userId,结算用户代理商的结算金额</br>
     * <p>该方法主要提供给 某个用户从普通用户转代理时调用
     * 在代理审批通过时，将申请代理人的订单结算金额结算给当前申请人归属的前一个代理<br>
     * 原因：在没成为代理之前，用户归属为前一个代理用户之下，该用户产生的订单佣金归属于前一个代理用户</p>
     * <p>产生误差：因结算时间没有考虑退款情况(正常逻辑考虑了延迟时间，此处是实时结算），
     * 可能造成这几天内如果发生退款，佣金确已结算给上一个代理用户的情况，因为这种情况产生的概率低，且本身
     * 佣金数额低，此误差暂时忽略，后续通过定时任务去处理这种异常结算的佣金,联系代理协商</p>
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean settlementPreviousAgency(Integer userId) {

        // 获取当前用户是否有未结算的订单(约束：已支付，且无退款，在正常流转的订单)，如果存在则结算给用户的代理人，如果不存在，则结束
        BigDecimal toSettleMoney = accountMapperEx.getUserUnOrderSettleMoney(userId);
        if (toSettleMoney == null || toSettleMoney.compareTo(new BigDecimal("0")) == 0) {// 如果该用户未产生订单
            log.info("用户 userId:{} 不存在未结算的订单,给其代理人结算佣金结束!", userId);
            return true;
        }
        // 获取当前用户的代理
        DtsUser user = userMapper.selectByPrimaryKey(userId);
        Integer sharedUserId = user.getShareUserId();
        // 获取用户账户信息并更新记录
        DtsUserAccount account = this.findShareUserAccountByUserId(sharedUserId);

        // 更新用户订单结算状态
        accountMapperEx.setUserOrderSettleStaus(userId);

        // 更新代理用户账号信息
        account.setRemainAmount(account.getRemainAmount().add(toSettleMoney));// 剩余结算,尚未结算给用户
        account.setTotalAmount(account.getTotalAmount().add(toSettleMoney));
        account.setModifyTime(LocalDateTime.now());
        userAccountMapper.updateByPrimaryKeySelective(account);

        return true;
    }

}
