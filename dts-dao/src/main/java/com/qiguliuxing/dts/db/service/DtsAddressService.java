package com.qiguliuxing.dts.db.service;

import com.qiguliuxing.dts.db.dao.DtsAddressMapper;
import com.qiguliuxing.dts.db.domain.DtsAddress;
import com.qiguliuxing.dts.db.util.PageResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@AllArgsConstructor
public class DtsAddressService {
    private final DtsAddressMapper addressMapper;

    public List<DtsAddress> queryByUid(Integer uid) {
        return addressMapper.findByUserId(uid);
    }

    public DtsAddress findById(Integer id) {
        return addressMapper.selectByPrimaryKey(id);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int add(DtsAddress address) {
        address.setAddTime(LocalDateTime.now());
        address.setUpdateTime(LocalDateTime.now());
        return addressMapper.insertSelective(address);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public int update(DtsAddress address) {
        address.setUpdateTime(LocalDateTime.now());
        return addressMapper.updateByPrimaryKeySelective(address);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(Integer id) {
        addressMapper.logicalDeleteByPrimaryKey(id);
    }

    public DtsAddress findDefault(Integer userId) {
        return addressMapper.findDefaultByUserId(userId);
    }

    /**
     * 取消用户的默认地址配置
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void resetDefault(Integer userId) {
        addressMapper.resetDefault(userId);
    }

    public PageResult<DtsAddress> querySelective(Integer userId, String name, Integer page, Integer limit, String sort,
                                                 String order) {
        page = page != null && page > 0 ? page : 1;
        int offset = (page - 1) * limit;
        long total = addressMapper.countByUserIdOrName(userId, name);
        List<DtsAddress> result = addressMapper.findByUserIdOrName(userId, name, offset, limit, sort, order);
        return PageResult.of(total, result, page, limit);
    }
}
