<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qiguliuxing.dts.db.dao.DtsGrouponRulesMapper">
    <resultMap id="BaseResultMap" type="com.qiguliuxing.dts.db.domain.DtsGrouponRules">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="pic_url" jdbcType="VARCHAR" property="picUrl"/>
        <result column="discount" jdbcType="DECIMAL" property="discount"/>
        <result column="discount_member" jdbcType="INTEGER" property="discountMember"/>
        <result column="add_time" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="deleted" jdbcType="BIT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
            goods_id,
            goods_name,
            pic_url,
            discount,
            discount_member,
            add_time,
            update_time,
            expire_time,
            deleted
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_groupon_rules
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from dts_groupon_rules
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.qiguliuxing.dts.db.domain.DtsGrouponRules">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_groupon_rules (goods_id, goods_name, pic_url,
        discount, discount_member, add_time,
        update_time, expire_time, deleted
        )
        values (#{goodsId,jdbcType=BIGINT}, #{goodsName,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR},
        #{discount,jdbcType=DECIMAL}, #{discountMember,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{expireTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.qiguliuxing.dts.db.domain.DtsGrouponRules">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into dts_groupon_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodsId != null">
                goods_id,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="picUrl != null">
                pic_url,
            </if>
            <if test="discount != null">
                discount,
            </if>
            <if test="discountMember != null">
                discount_member,
            </if>
            <if test="addTime != null">
                add_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="expireTime != null">
                expire_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="picUrl != null">
                #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="discount != null">
                #{discount,jdbcType=DECIMAL},
            </if>
            <if test="discountMember != null">
                #{discountMember,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.qiguliuxing.dts.db.domain.DtsGrouponRules">
        update dts_groupon_rules
        <set>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="picUrl != null">
                pic_url = #{picUrl,jdbcType=VARCHAR},
            </if>
            <if test="discount != null">
                discount = #{discount,jdbcType=DECIMAL},
            </if>
            <if test="discountMember != null">
                discount_member = #{discountMember,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                add_time = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.qiguliuxing.dts.db.domain.DtsGrouponRules">
        update dts_groupon_rules
        set goods_id        = #{goodsId,jdbcType=BIGINT},
            goods_name      = #{goodsName,jdbcType=VARCHAR},
            pic_url         = #{picUrl,jdbcType=VARCHAR},
            discount        = #{discount,jdbcType=DECIMAL},
            discount_member = #{discountMember,jdbcType=INTEGER},
            add_time        = #{addTime,jdbcType=TIMESTAMP},
            update_time     = #{updateTime,jdbcType=TIMESTAMP},
            expire_time     = #{expireTime,jdbcType=TIMESTAMP},
            deleted         = #{deleted,jdbcType=BIT}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_groupon_rules
        where id = #{id} and deleted = 0
    </select>

    <select id="findByGoodsId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_groupon_rules
        where goods_id = #{goodsId} and deleted = 0
    </select>

    <select id="querySelective" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dts_groupon_rules
        where deleted = 0
        <if test="goodsId != null">
            and goods_id = #{goodsId}
        </if>
        <if test="orderByClause != null and orderByClause != ''">
            order by ${orderByClause}
        </if>
        <if test="limit != null and offset != null">
            limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>
    <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
        update dts_groupon_rules
        set deleted = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="counts" resultType="int">
        select count(0) form dts_groupon_rules
        where deleted = 0
    </select>

    <select id="countByGoodsId" resultType="int">
        select count(0)
        from dts_groupon_rules
        <where>
            <if test="goodsId != null">
                goods_id = #{goodsId}
            </if>
            and deleted = 0
        </where>
    </select>
</mapper>
