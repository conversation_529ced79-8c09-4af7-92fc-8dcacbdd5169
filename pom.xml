<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.qiguliuxing</groupId>
    <artifactId>dts</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>11</java.version>
        <maven.test.skip>true</maven.test.skip>
        <fastjson.version>1.2.83</fastjson.version>
        <jackson-databind.version>2.13.5</jackson-databind.version>
        <mybatis-spring-boot-starter.version>2.3.1</mybatis-spring-boot-starter.version>
        <shiro-spring-boot-web-starter.version>1.11.0</shiro-spring-boot-web-starter.version>
        <hibernate-validator.version>6.2.5.Final</hibernate-validator.version>
        <mysql-connector-java.version>8.0.33</mysql-connector-java.version>
        <weixin-java-pay.version>4.6.0</weixin-java-pay.version>
        <weixin-java-miniapp.version>4.6.0</weixin-java-miniapp.version>
        <qcloudsms.version>1.0.5</qcloudsms.version>
        <cos_api.version>5.6.8</cos_api.version>
        <aliyun-sdk-oss.version>2.5.0</aliyun-sdk-oss.version>
        <qiniu-java-sdk.version>[7.2.0, 7.2.99]</qiniu-java-sdk.version>
        <spring-boot-starter-json.version>2.7.18</spring-boot-starter-json.version>
        <spring-boot-starter-mail.version>2.7.18</spring-boot-starter-mail.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <modules>
        <module>dts-admin-api</module>
        <module>dts-core</module>
        <module>dts-dao</module>
        <module>dts-wx-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Mybatis 依赖 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot-starter.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring-boot-web-starter</artifactId>
                <version>${shiro-spring-boot-web-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>23.0</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <!-- MySQL 连接驱动依赖 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-pay-spring-boot-starter</artifactId>
                <version>${weixin-java-pay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
                <version>${weixin-java-pay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-mp-spring-boot-starter</artifactId>
                <version>${weixin-java-pay.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.qcloudsms</groupId>
                <artifactId>qcloudsms</artifactId>
                <version>${qcloudsms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qcloud</groupId>
                <artifactId>cos_api</artifactId>
                <version>${cos_api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu-java-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring-boot-starter-json.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-mail</artifactId>
                <version>${spring-boot-starter-mail.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 测试模块 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!-- 配置阿里云Maven镜像源 -->
    <repositories>
        <repository>
            <id>aliyun</id>
            <name>Aliyun Maven Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <name>Aliyun Maven Plugin Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <!-- dev 环境 -->
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.active>dev</profile.active>
                <!-- 各个服务的端口配置 -->
                <api.wx.port>8080</api.wx.port>
                <api.admin.port>8083</api.admin.port>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
        <profile>
            <!-- test-online 环境 -->
            <id>test-online</id>
            <properties>
                <profile.active>demo</profile.active>
                <!-- 各个服务的端口配置 -->
                <api.wx.port>8080</api.wx.port>
                <api.admin.port>8083</api.admin.port>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
        <profile>
            <!-- prod 环境 -->
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
                <!-- 各个服务的端口配置 -->
                <api.wx.port>8080</api.wx.port>
                <api.admin.port>8083</api.admin.port>
                <logback.dir>./logs</logback.dir>
            </properties>
        </profile>
    </profiles>
</project>
